// // Example showing the simplified DSL syntax with auto-generated event names
// using System;
// using <PERSON>onLoader;
//
// // Target classes to patch
// public class MinimapFogOfWar
// {
//     public void Initialize()
//     {
//         MelonLogger.Msg("MinimapFogOfWar initialized");
//     }
//     
//     public void UpdateFogOfWar()
//     {
//         MelonLogger.Msg("Updating fog of war");
//     }
//     
//     public void ClearFog()
//     {
//         MelonLogger.Msg("Clearing fog");
//     }
// }
//
// public class Player
// {
//     public void TakeDamage(float amount)
//     {
//         MelonLogger.Msg($"Player taking {amount} damage");
//     }
//     
//     public void Die()
//     {
//         MelonLogger.Msg("Player died");
//     }
// }
//
// // Your patches.autopatch file now looks super clean:
// /*
// # AutoPatch DSL Configuration File
// # Simple syntax: TargetType.MethodName -> PatchType
// # Event names are automatically generated as: On{MethodName}{PatchType}
//
// # Example patches
// MinimapFogOfWar.Initialize -> Postfix         # Creates: OnInitializePostfix
// MinimapFogOfWar.UpdateFogOfWar -> Postfix     # Creates: OnUpdateFogOfWarPostfix
// MinimapFogOfWar.ClearFog -> Prefix            # Creates: OnClearFogPrefix
//
// # Player patches
// Player.TakeDamage -> Prefix                   # Creates: OnTakeDamagePrefix
// Player.Die -> Postfix                         # Creates: OnDiePostfix
// */
//
// // Usage - subscribe to the auto-generated events
// public class SimplifiedPatchExample
// {
//     public static void InitializePatches()
//     {
//         // Event names are automatically generated based on method name + patch type
//         
//         // MinimapFogOfWar.Initialize -> Postfix creates "OnInitializePostfix"
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnInitializePostfix += (instance) =>
//         {
//             MelonLogger.Msg("[Event] MinimapFogOfWar initialized!");
//         };
//         
//         // MinimapFogOfWar.UpdateFogOfWar -> Postfix creates "OnUpdateFogOfWarPostfix"
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnUpdateFogOfWarPostfix += (instance) =>
//         {
//             MelonLogger.Msg("[Event] Fog of war updated!");
//         };
//         
//         // MinimapFogOfWar.ClearFog -> Prefix creates "OnClearFogPrefix"
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnClearFogPrefix += (instance) =>
//         {
//             MelonLogger.Msg("[Event] About to clear fog!");
//             return true; // Allow original method to run
//         };
//         
//         // Player.TakeDamage -> Prefix creates "OnTakeDamagePrefix"
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnTakeDamagePrefix += (instance, amount) =>
//         {
//             MelonLogger.Msg($"[Event] Player about to take {amount} damage!");
//             
//             if (amount > 50)
//             {
//                 MelonLogger.Msg("[Event] Blocking high damage!");
//                 return false; // Cancel original method
//             }
//             
//             return true; // Allow original method
//         };
//         
//         // Player.Die -> Postfix creates "OnDiePostfix"
//         AutoPatch.Generated.Generated_DSLGenerated_Patches.OnDiePostfix += (instance) =>
//         {
//             MelonLogger.Msg("[Event] Player died! Triggering respawn...");
//         };
//     }
// }
//
// // Benefits of the simplified syntax:
// // ✅ No manual event naming - completely automatic
// // ✅ Consistent naming convention: On{MethodName}{PatchType}
// // ✅ Less typing and fewer mistakes
// // ✅ Cleaner DSL files
// // ✅ Focus on what matters: what to patch and when
// // ✅ Event names are predictable and discoverable
//
// // Event naming examples:
// // Player.Move -> Prefix        = OnMovePrefix
// // Enemy.Attack -> Postfix      = OnAttackPostfix  
// // GameManager.Update -> Transpiler = OnUpdateTranspiler
// // WeaponSystem.Fire -> Finalizer   = OnFireFinalizer
