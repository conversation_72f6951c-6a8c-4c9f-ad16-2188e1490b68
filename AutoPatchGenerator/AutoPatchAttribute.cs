// ReSharper disable All
// AutoPatchAttribute.cs - Simple attribute, no parameters needed
using System;
using System.Linq;

[System.AttributeUsage(System.AttributeTargets.Method)]
#pragma warning disable CA1050
public class AutoPatchAttribute : System.Attribute
#pragma warning restore CA1050
{
    public string TargetType { get; set; }
    public string TargetMethod { get; set; }
    
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    public AutoPatchAttribute() { }
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    
    public AutoPatchAttribute(string targetType, string targetMethod)
    {
        TargetType = targetType;
        TargetMethod = targetMethod;
    }
    
#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    public AutoPatchAttribute(string fullTarget)
#pragma warning restore CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider adding the 'required' modifier or declaring as nullable.
    {
        var parts = fullTarget.Split('.');
        if (parts.Length >= 2)
        {
            TargetMethod = parts[parts.Length - 1];
            TargetType = string.Join(".", parts.Take(parts.Length - 1));
        }
    }
}
