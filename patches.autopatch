# AutoPatch DSL Configuration File
# Simple syntax: TargetType.MethodName -> PatchType
# Event names are automatically generated as: On{MethodName}{PatchType}

# Example patches
MinimapFogOfWar.Initialize -> Postfix         # Creates: OnInitializePostfix
MinimapFogOfWar.UpdateFogOfWar -> Postfix     # Creates: OnUpdateFogOfWarPostfix
MinimapFogOfWar.ClearFog -> Prefix            # Creates: OnClearFogPrefix

# Player patches
Player.TakeDamage -> Prefix                   # Creates: OnTakeDamagePrefix
Player.Die -> Postfix                         # Creates: OnDiePostfix

# Enemy patches
Enemy.Attack -> Prefix                        # Creates: OnAttackPrefix
Enemy.Die -> Postfix                          # Creates: OnDiePostfix